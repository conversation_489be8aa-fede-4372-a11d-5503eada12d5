#!/usr/bin/env python3
# coding=utf-8
"""
Test script to demonstrate the enhanced error handling and graceful shutdown functionality.
This script simulates database connection failures and shows how the system handles them.
"""

import asyncio
import io
import logging
import sys

import pandas as pd
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import InterfaceError

# Import the enhanced functions
from utility_code import (
    upsert_async_with_health_check,
    upsert_async,
    GracefulShutdownRequested,
    DatabaseConnectionError,
    MaxRetriesExceededError,
    is_shutdown_requested,
    request_graceful_shutdown
)

# Replace sys.stdout with UTF-8 wrapped stream
# sys.stdout = io.TextIOWrapper(sys.stdout.detach(), encoding='utf-8', errors='replace')
# sys.stderr = io.TextIOWrapper(sys.stderr.detach(), encoding='utf-8', errors='replace')

try:
    # Check if we're on Windows and need to wrap streams
    if sys.platform.startswith('win') and hasattr(sys.stdout, 'detach'):
        # Only wrap if not already wrapped
        if not hasattr(sys.stdout, 'buffer') or sys.stdout.encoding.lower() != 'utf-8':
            sys.stdout = io.TextIOWrapper(
                sys.stdout.detach(),
                encoding='utf-8',
                errors='replace'  # This prevents crashes on encoding errors
            )
        if not hasattr(sys.stderr, 'buffer') or sys.stderr.encoding.lower() != 'utf-8':
            sys.stderr = io.TextIOWrapper(
                sys.stderr.detach(),
                encoding='utf-8',
                errors='replace'
            )
except (AttributeError, OSError):
    # Fallback: streams might already be wrapped or unavailable
    pass

# Setup logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Add handler if needed
if not logger.handlers:
    handler = logging.StreamHandler(sys.stdout)
    logger.addHandler(handler)

class MockModel:
    """Mock SQLAlchemy model for testing"""
    __table__ = MagicMock()
    __table__.name = "test_table"


async def test_connection_timeout_error():
    """Test handling of connection timeout errors"""
    logger.info("=== Testing Connection Timeout Error ===")
    
    # Create mock session that raises TimeoutError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = TimeoutError("Connection timed out")
    
    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=3,
            retry_delay=0.1,  # Fast retry for testing
            my_logger=logger
        )
        logger.error("Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        logger.info(f"✅ Graceful shutdown correctly requested: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected exception: {e}")


async def test_cancelled_error():
    """Test handling of CancelledError (asyncio cancellation)"""
    logger.info("=== Testing Cancelled Error ===")
    
    # Create mock session that raises CancelledError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = asyncio.CancelledError("Operation was cancelled")
    
    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=3,
            retry_delay=0.1,
            my_logger=logger
        )
        logger.error("Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        logger.info(f"✅ Graceful shutdown correctly requested: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected exception: {e}")


async def test_interface_error():
    """Test handling of SQLAlchemy InterfaceError"""
    logger.info("=== Testing Interface Error ===")
    
    # Create mock session that raises InterfaceError
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = InterfaceError("Connection is closed", None, None)
    
    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    try:
        await upsert_async(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            max_retries=2,
            retry_delay=0.1,
            my_logger=logger
        )
        logger.error("Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested as e:
        logger.info(f"✅ Graceful shutdown correctly requested: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected exception: {e}")


async def test_health_check_wrapper():
    """Test the health check wrapper function"""
    logger.info("=== Testing Health Check Wrapper ===")
    
    # Create mock session that fails health check
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.side_effect = Exception("Health check failed")
    
    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    try:
        await upsert_async_with_health_check(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            my_logger=logger
        )
        logger.error("Expected DatabaseConnectionError but function completed normally")
    except DatabaseConnectionError as e:
        logger.info(f"✅ Database connection error correctly detected: {e}")
    except GracefulShutdownRequested as e:
        logger.info(f"✅ Graceful shutdown correctly requested: {e}")
    except Exception as e:
        logger.error(f"❌ Unexpected exception: {e}")


async def test_successful_operation():
    """Test successful operation without errors"""
    logger.info("=== Testing Successful Operation ===")
    
    # Create mock session that succeeds
    mock_session = AsyncMock(spec=AsyncSession)
    mock_session.execute.return_value = MagicMock()
    
    # Mock the health check to succeed
    mock_result = AsyncMock()
    mock_result.fetchone.return_value = (1,)
    mock_session.execute.return_value = mock_result
    
    # Create test data
    test_data = pd.DataFrame({'id': [1, 2, 3], 'name': ['A', 'B', 'C']})
    
    try:
        await upsert_async_with_health_check(
            session=mock_session,
            model=MockModel,
            rows=test_data,
            my_logger=logger
        )
        logger.info("✅ Successful operation completed without errors")
    except Exception as e:
        logger.error(f"❌ Unexpected exception in successful operation: {e}")


async def test_shutdown_flag():
    """Test the global shutdown flag functionality"""
    logger.info("=== Testing Shutdown Flag ===")
    
    # Reset shutdown flag
    import utility_code
    utility_code._shutdown_requested = False
    
    # Check initial state
    assert not is_shutdown_requested(), "Shutdown flag should be False initially"
    logger.info("✅ Initial shutdown flag state is correct")
    
    # Request shutdown
    try:
        request_graceful_shutdown("Test shutdown request", logger)
        logger.error("Expected GracefulShutdownRequested but function completed normally")
    except GracefulShutdownRequested:
        logger.info("✅ Graceful shutdown correctly requested")
    
    # Check flag is now set
    assert is_shutdown_requested(), "Shutdown flag should be True after request"
    logger.info("✅ Shutdown flag correctly set after request")


async def main():
    """Run all tests"""
    logger.info("Starting enhanced error handling tests...")
    
    tests = [
        test_shutdown_flag,
        test_successful_operation,
        test_health_check_wrapper,
        test_connection_timeout_error,
        test_cancelled_error,
        test_interface_error,
    ]
    
    for test in tests:
        try:
            await test()
            logger.info(f"✅ {test.__name__} completed")
        except Exception as e:
            logger.error(f"❌ {test.__name__} failed: {e}")
        
        # Add a small delay between tests
        await asyncio.sleep(0.1)
        logger.info("-" * 50)
    
    logger.info("All tests completed!")

# Solution 2: Force UTF-8 for stdout/stderr (Your current approach, improved)
def setup_utf8_output():
    """Setup UTF-8 encoding for stdout and stderr on Windows"""
    try:
        # Check if we're on Windows and need to wrap streams
        if sys.platform.startswith('win') and hasattr(sys.stdout, 'detach'):
            # Only wrap if not already wrapped
            if not hasattr(sys.stdout, 'buffer') or sys.stdout.encoding.lower() != 'utf-8':
                sys.stdout = io.TextIOWrapper(
                    sys.stdout.detach(),
                    encoding='utf-8',
                    errors='replace'  # This prevents crashes on encoding errors
                )
            if not hasattr(sys.stderr, 'buffer') or sys.stderr.encoding.lower() != 'utf-8':
                sys.stderr = io.TextIOWrapper(
                    sys.stderr.detach(),
                    encoding='utf-8',
                    errors='replace'
                )
    except (AttributeError, OSError):
        # Fallback: streams might already be wrapped or unavailable
        pass


# Solution 3: Custom Logging Formatter with Fallback
class SafeUnicodeFormatter(logging.Formatter):
    """Custom formatter that handles Unicode characters safely"""

    def format(self, record):
        try:
            return super().format(record)
        except UnicodeEncodeError:
            # Fallback: replace problematic characters
            msg = super().format(record)
            return msg.encode('ascii', errors='replace').decode('ascii')

if __name__ == "__main__":
    setup_utf8_output()
    # Setup logging with safe formatter
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

    # Override the formatter
    for handler in logging.getLogger().handlers:
        handler.setFormatter(SafeUnicodeFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        ))

    logger = logging.getLogger(__name__)

    # Now this should work
    logger.info("✅ Test completed successfully")

    asyncio.run(main())
