# Enhanced Error Handling and Graceful Shutdown

This document describes the enhanced error handling and graceful shutdown mechanisms implemented to address database connection failures and prevent data corruption.

## Problem Statement

The original error was a `TimeoutError` and `CancelledError` in asyncpg connection handling:

```
2025-06-25T14:26:22.397 ERROR    wrapper                         upsert_async                                       41   | [DEBUG] Error in upsert_async after 142.07s: 
asyncio.exceptions.CancelledError
TimeoutError
```

This error occurred during database operations and could lead to:
- Partial data updates
- Data corruption
- Process hanging indefinitely
- Inconsistent database state

## Solution Overview

The enhanced error handling system provides:

1. **Comprehensive Error Detection** - Identifies various connection and timeout errors
2. **Intelligent Retry Logic** - Exponential backoff with jitter for connection errors
3. **Graceful Shutdown** - Controlled process termination to prevent data corruption
4. **Session Health Checks** - Proactive connection validation
5. **Transaction Integrity** - Coordinated commits and rollbacks

## Key Components

### 1. Enhanced Exception Classes

```python
class DatabaseConnectionError(Exception):
    """Raised when database connection fails"""
    pass

class MaxRetriesExceededError(Exception):
    """Raised when maximum retry attempts are exhausted"""
    pass

class GracefulShutdownRequested(Exception):
    """Raised when graceful shutdown is requested due to critical errors"""
    pass
```

### 2. Graceful Shutdown Mechanism

```python
def request_graceful_shutdown(reason: str, logger: Logger):
    """Request graceful shutdown of the process"""
    global _shutdown_requested
    if not _shutdown_requested:
        _shutdown_requested = True
        logger.critical(f"GRACEFUL SHUTDOWN REQUESTED: {reason}")
        logger.critical("Process will terminate to prevent data corruption")
    raise GracefulShutdownRequested(reason)
```

### 3. Enhanced upsert_async Function

The `upsert_async` function now includes:

- **Comprehensive Error Detection**: Detects timeout, cancellation, and connection errors
- **Exponential Backoff**: Intelligent retry delays with jitter
- **Graceful Shutdown**: Triggers shutdown when max retries exceeded
- **Detailed Logging**: Enhanced error reporting and debugging information

### 4. Session Health Check Wrapper

```python
async def upsert_async_with_health_check(session, model, rows, **kwargs):
    """
    Wrapper around upsert_async that includes session health checks 
    and graceful error handling.
    """
```

## Error Handling Flow

```mermaid
graph TD
    A[Database Operation] --> B{Connection Error?}
    B -->|Yes| C{Retries Left?}
    B -->|No| D[Operation Success]
    C -->|Yes| E[Exponential Backoff]
    C -->|No| F[Max Retries Exceeded]
    E --> G[Retry Operation]
    G --> A
    F --> H[Request Graceful Shutdown]
    H --> I[Log Critical Error]
    I --> J[Raise GracefulShutdownRequested]
    J --> K[Process Termination]
```

## Enhanced Error Detection

The system now detects these error types:

### Connection Errors (Retryable)
- `InterfaceError`
- `DisconnectionError` 
- `TimeoutError`
- `CancelledError`
- Connection closed/terminated messages
- Network unreachable errors

### Critical Errors (Trigger Shutdown)
- Max retries exceeded for connection errors
- Session health check failures
- Transaction commit failures
- Unexpected database errors

## Usage Examples

### Basic Usage with Health Check

```python
# Use the wrapper function for automatic health checks
await upsert_async_with_health_check(
    session=pg_async_session,
    model=Issue,
    rows=df,
    my_logger=logger
)
```

### Direct Usage with Custom Settings

```python
# Direct usage with custom retry settings
await upsert_async(
    session=pg_async_session,
    model=Issue,
    rows=df,
    max_retries=10,
    retry_delay=2.0,
    enable_graceful_shutdown=True,
    my_logger=logger
)
```

### Handling Graceful Shutdown

```python
try:
    await process_jira_issues(project_key, scope, initial_load)
except GracefulShutdownRequested as e:
    logger.critical(f"Graceful shutdown requested: {e}")
    # Perform cleanup operations
    await cleanup_resources()
    # Exit gracefully
    sys.exit(1)
```

## Process-Level Integration

### Consumer Functions

The `consume_issues` and `_process_issues_loop` functions now:

- Check for shutdown requests before processing
- Handle `GracefulShutdownRequested` exceptions
- Commit transactions only after successful completion
- Rollback on errors or shutdown requests

### Main Process Function

The `process_jira_issues` function now:

- Checks shutdown status at key points
- Handles graceful shutdown during initialization
- Ensures transaction integrity during shutdown
- Performs coordinated rollbacks when needed

## Configuration Options

### Retry Settings

```python
max_retries = 5          # Maximum retry attempts
retry_delay = 1.0        # Base delay between retries (exponential backoff applied)
enable_graceful_shutdown = True  # Whether to trigger shutdown on max retries
```

### Health Check Settings

```python
# Session health check timeout
health_check_timeout = 30  # seconds
```

## Monitoring and Logging

### Log Levels

- **DEBUG**: Detailed operation information
- **INFO**: Normal operation status
- **WARNING**: Recoverable errors and retries
- **ERROR**: Non-critical errors
- **CRITICAL**: Errors requiring graceful shutdown

### Key Log Messages

```
GRACEFUL SHUTDOWN REQUESTED: Database upsert failed after 5 retries for table issues
All 5 retry attempts exhausted for upsert operation on table issues
Connection error detected. Retrying in 2.10 seconds... (attempt 2/5)
Session health check failed: Connection timeout
```

## Testing

Run the test script to verify error handling:

```bash
python dags/data_pipeline/test_graceful_shutdown.py
```

The test script covers:
- Connection timeout errors
- Cancellation errors
- Interface errors
- Health check failures
- Successful operations
- Shutdown flag functionality

## Best Practices

1. **Always use the wrapper function** `upsert_async_with_health_check` in production code
2. **Handle GracefulShutdownRequested** exceptions at the process level
3. **Monitor critical log messages** for early warning of issues
4. **Set appropriate retry limits** based on your use case
5. **Test error scenarios** regularly to ensure proper handling

## Migration Guide

To migrate existing code:

1. Replace `upsert_async` calls with `upsert_async_with_health_check`
2. Add `GracefulShutdownRequested` exception handling
3. Update logging to handle new error types
4. Test with simulated connection failures

## Future Enhancements

Potential improvements:
- Circuit breaker pattern for cascading failure prevention
- Metrics collection for error rates and retry patterns
- Integration with external monitoring systems
- Automatic recovery mechanisms
- Database connection pooling enhancements
