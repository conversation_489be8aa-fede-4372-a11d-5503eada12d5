#!/usr/bin/env python3
# coding=utf-8
"""
Example script showing how to run the enhanced JIRA processing with graceful shutdown handling.
This script demonstrates proper error handling and graceful shutdown integration.
"""

import asyncio
import logging
import signal
import sys
from typing import List

# Import the enhanced functions
from utility_code import (
    process_jira_issues,
    GracefulShutdownRequested,
    is_shutdown_requested,
    request_graceful_shutdown
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('jira_processing.log')
    ]
)
logger = logging.getLogger(__name__)


class GracefulShutdownHandler:
    """Handles graceful shutdown signals and cleanup"""
    
    def __init__(self):
        self.shutdown_requested = False
        self.setup_signal_handlers()
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        if sys.platform != 'win32':
            # Unix/Linux signal handling
            signal.signal(signal.SIGTERM, self.signal_handler)
            signal.signal(signal.SIGINT, self.signal_handler)
        else:
            # Windows signal handling
            signal.signal(signal.SIGINT, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.warning(f"Received signal {signum}, requesting graceful shutdown...")
        try:
            request_graceful_shutdown(f"Signal {signum} received", logger)
        except GracefulShutdownRequested:
            # Expected behavior
            pass
        self.shutdown_requested = True


async def process_projects_with_graceful_shutdown(projects: List[str], scope: str = "project", initial_load: bool = True):
    """
    Process multiple JIRA projects with graceful shutdown handling.
    
    Args:
        projects: List of project keys to process
        scope: Processing scope (default: "project")
        initial_load: Whether this is an initial load (default: True)
    """
    shutdown_handler = GracefulShutdownHandler()
    
    logger.info(f"Starting JIRA processing for projects: {projects}")
    logger.info(f"Scope: {scope}, Initial Load: {initial_load}")
    
    successful_projects = []
    failed_projects = []
    
    for project_key in projects:
        try:
            # Check for shutdown request before processing each project
            if is_shutdown_requested() or shutdown_handler.shutdown_requested:
                logger.warning(f"Shutdown requested, skipping remaining projects")
                break
            
            logger.info(f"Processing project: {project_key}")
            
            # Process the project with enhanced error handling
            await process_jira_issues(
                project_key=project_key,
                scope=scope,
                initial_load=initial_load
            )
            
            successful_projects.append(project_key)
            logger.info(f"✅ Successfully processed project: {project_key}")
            
        except GracefulShutdownRequested as e:
            logger.critical(f"Graceful shutdown requested during processing of {project_key}: {e}")
            failed_projects.append(project_key)
            break
            
        except Exception as e:
            logger.error(f"❌ Failed to process project {project_key}: {e}", exc_info=True)
            failed_projects.append(project_key)
            
            # Check if this is a critical error that should stop all processing
            if "database" in str(e).lower() or "connection" in str(e).lower():
                logger.critical(f"Critical error detected, stopping all processing")
                try:
                    request_graceful_shutdown(f"Critical error in {project_key}: {e}", logger)
                except GracefulShutdownRequested:
                    break
    
    # Summary
    logger.info("=" * 60)
    logger.info("PROCESSING SUMMARY")
    logger.info("=" * 60)
    logger.info(f"Total projects: {len(projects)}")
    logger.info(f"Successful: {len(successful_projects)} - {successful_projects}")
    logger.info(f"Failed: {len(failed_projects)} - {failed_projects}")
    
    if is_shutdown_requested() or shutdown_handler.shutdown_requested:
        logger.warning("Processing terminated due to graceful shutdown request")
        return False
    
    return len(failed_projects) == 0


async def single_project_example():
    """Example of processing a single project with error handling"""
    logger.info("=== Single Project Processing Example ===")
    
    project_key = "PLAT"
    
    try:
        await process_jira_issues(
            project_key=project_key,
            scope="project",
            initial_load=True
        )
        logger.info(f"✅ Successfully processed {project_key}")
        
    except GracefulShutdownRequested as e:
        logger.critical(f"Graceful shutdown requested: {e}")
        logger.info("Performing cleanup operations...")
        # Add any cleanup logic here
        logger.info("Cleanup completed, exiting gracefully")
        return False
        
    except Exception as e:
        logger.error(f"❌ Error processing {project_key}: {e}", exc_info=True)
        return False
    
    return True


async def multiple_projects_example():
    """Example of processing multiple projects with error handling"""
    logger.info("=== Multiple Projects Processing Example ===")
    
    projects = ["PLAT", "CPP", "PLP"]
    
    success = await process_projects_with_graceful_shutdown(
        projects=projects,
        scope="project",
        initial_load=True
    )
    
    if success:
        logger.info("✅ All projects processed successfully")
    else:
        logger.warning("⚠️ Some projects failed or shutdown was requested")
    
    return success


async def main():
    """Main function demonstrating different usage patterns"""
    logger.info("Starting JIRA processing with enhanced error handling...")
    
    try:
        # Example 1: Single project processing
        logger.info("\n" + "=" * 60)
        success1 = await single_project_example()
        
        # Only proceed if first example succeeded and no shutdown requested
        if success1 and not is_shutdown_requested():
            # Example 2: Multiple projects processing
            logger.info("\n" + "=" * 60)
            success2 = await multiple_projects_example()
        else:
            logger.info("Skipping multiple projects example due to previous failure or shutdown")
            success2 = False
        
        # Final status
        if success1 and success2:
            logger.info("🎉 All examples completed successfully!")
            return 0
        else:
            logger.warning("⚠️ Some examples failed or were interrupted")
            return 1
            
    except GracefulShutdownRequested as e:
        logger.critical(f"Graceful shutdown requested at main level: {e}")
        logger.info("Performing final cleanup...")
        # Add any final cleanup logic here
        logger.info("Final cleanup completed")
        return 2
        
    except KeyboardInterrupt:
        logger.warning("Keyboard interrupt received, requesting graceful shutdown...")
        try:
            request_graceful_shutdown("Keyboard interrupt", logger)
        except GracefulShutdownRequested:
            pass
        return 3
        
    except Exception as e:
        logger.error(f"Unexpected error in main: {e}", exc_info=True)
        return 4


if __name__ == "__main__":
    # Set event loop policy for Windows
    if sys.platform.startswith('win'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    
    try:
        exit_code = asyncio.run(main())
        logger.info(f"Process exiting with code: {exit_code}")
        sys.exit(exit_code)
        
    except KeyboardInterrupt:
        logger.warning("Keyboard interrupt during shutdown")
        sys.exit(130)  # Standard exit code for Ctrl+C
        
    except Exception as e:
        logger.critical(f"Fatal error during execution: {e}", exc_info=True)
        sys.exit(1)
